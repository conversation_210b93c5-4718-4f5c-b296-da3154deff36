# Design Patterns Mini Project

A comprehensive Java application demonstrating five classic design patterns using JavaFX for the user interface.

## Design Patterns Implemented

### 1. Singleton Pattern
- **Class**: `LoggerSingleton`
- **Purpose**: Ensures only one logger instance exists throughout the application
- **Features**: Thread-safe implementation with configurable logging strategies

### 2. Strategy Pattern
- **Classes**: `DrawingStrategy`, `CircleDrawingStrategy`, `RectangleDrawingStrategy`, `LoggerStrategy`, `ConsoleLogger`, `FileLogger`, `DBLogger`
- **Purpose**: Allows switching between different algorithms at runtime
- **Features**: 
  - Different drawing strategies for shapes
  - Different logging strategies (console, file, database)

### 3. Observer Pattern
- **Classes**: `DrawingSubject`, `DrawingObserver`, `LogObserver`
- **Purpose**: Notifies multiple observers when drawing events occur
- **Features**: Automatic logging of all drawing activities

### 4. Decorator Pattern
- **Classes**: `ShapeDecorator`, `BorderDecorator`
- **Purpose**: Adds additional functionality to shapes without modifying their structure
- **Features**: Adds decorative borders around any shape

### 5. Adapter Pattern
- **Classes**: `ShapeAdapter`
- **Purpose**: Adapts legacy drawing methods to work with the new strategy interface
- **Features**: Integrates triangle and diamond shapes from a legacy system

## Project Structure

```
src/designPattern_MiniProjet/
├── LoggerSingleton.java          # Singleton pattern
├── LoggerStrategy.java           # Strategy interface for logging
├── ConsoleLogger.java            # Console logging strategy
├── FileLogger.java               # File logging strategy
├── DBLogger.java                 # Database logging strategy
├── DrawingStrategy.java          # Strategy interface for drawing
├── CircleDrawingStrategy.java    # Circle drawing strategy
├── RectangleDrawingStrategy.java # Rectangle drawing strategy
├── DrawingSubject.java           # Observer pattern subject
├── DrawingObserver.java          # Observer pattern observer
├── LogObserver.java              # Concrete observer for logging
├── ShapeDecorator.java           # Decorator pattern base
├── BorderDecorator.java          # Concrete decorator
├── ShapeAdapter.java             # Adapter pattern implementation
├── DrawingBoard.java             # Main drawing canvas
├── test.java                     # JavaFX application
└── PatternTest.java              # Core pattern testing
```

## How to Compile and Run

### Prerequisites
- Java 17 or higher
- JavaFX SDK 21.0.7 (or compatible version)

### Compilation

1. **Compile core patterns (without JavaFX):**
   ```bash
   javac -d bin src/designPattern_MiniProjet/LoggerStrategy.java src/designPattern_MiniProjet/ConsoleLogger.java src/designPattern_MiniProjet/FileLogger.java src/designPattern_MiniProjet/DBLogger.java src/designPattern_MiniProjet/LoggerSingleton.java src/designPattern_MiniProjet/DrawingObserver.java src/designPattern_MiniProjet/DrawingSubject.java src/designPattern_MiniProjet/LogObserver.java src/designPattern_MiniProjet/PatternTest.java
   ```

2. **Compile full JavaFX application:**
   ```bash
   javac --module-path "path/to/javafx/lib" -d bin src/designPattern_MiniProjet/*.java src/module-info.java
   ```

### Running

1. **Run core pattern tests:**
   ```bash
   java -cp bin designPattern_MiniProjet.PatternTest
   ```

2. **Run JavaFX application:**
   ```bash
   java --module-path "path/to/javafx/lib" --add-modules javafx.controls,javafx.fxml -cp bin designPattern_MiniProjet.test
   ```

## Features

### Interactive GUI
- Drawing canvas with multiple shape options
- Strategy selection dropdown
- Logger strategy selection
- Real-time pattern demonstration

### Pattern Demonstrations
- **Strategy**: Switch between different drawing and logging strategies
- **Observer**: Automatic logging of all drawing events
- **Decorator**: Add borders to any shape
- **Adapter**: Use legacy shapes (triangle, diamond) with modern interface
- **Singleton**: Single logger instance used throughout

### Logging
- Console output with timestamps
- File logging with automatic file creation
- Mock database logging
- Configurable logging levels

## Testing

The `PatternTest.java` class provides unit tests for core patterns:
- Singleton instance verification
- Strategy pattern switching
- Observer notifications
- Pattern integration testing

## Educational Value

This project demonstrates:
- Proper implementation of classic design patterns
- Pattern interaction and composition
- Real-world application of design principles
- Clean code architecture
- Separation of concerns
- Extensible design

## Future Enhancements

- Add more shape types
- Implement Command pattern for undo/redo
- Add Factory pattern for shape creation
- Implement State pattern for drawing modes
- Add more decorator types (color, shadow, etc.)
