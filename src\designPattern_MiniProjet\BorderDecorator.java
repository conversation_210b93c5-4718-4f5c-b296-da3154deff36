package designPattern_MiniProjet;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

/**
 * Concrete Decorator that adds a decorative border around shapes
 */
public class BorderDecorator extends ShapeDecorator {
    private Color borderColor;
    private double borderWidth;

    public BorderDecorator(DrawingStrategy strategy) {
        super(strategy);
        this.borderColor = Color.GOLD;
        this.borderWidth = 5;
    }

    public BorderDecorator(DrawingStrategy strategy, Color borderColor, double borderWidth) {
        super(strategy);
        this.borderColor = borderColor;
        this.borderWidth = borderWidth;
    }

    @Override
    public void draw(GraphicsContext gc, double x, double y, double width, double height) {
        // Draw the original shape
        super.draw(gc, x, y, width, height);

        // Add decorative border
        gc.setStroke(borderColor);
        gc.setLineWidth(borderWidth);

        // Draw border around the shape with some padding
        double padding = borderWidth / 2;
        gc.strokeRect(x - padding, y - padding, width + 2 * padding, height + 2 * padding);

        // Log the decoration
        LoggerSingleton.getInstance().log("DECORATOR", "Added border decoration to " + getShapeName());
    }

    @Override
    public String getShapeName() {
        return "Bordered " + super.getShapeName();
    }
}
