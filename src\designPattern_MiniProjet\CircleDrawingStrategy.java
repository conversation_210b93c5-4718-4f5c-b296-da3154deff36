package designPattern_MiniProjet;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

/**
 * Concrete Strategy for drawing circles
 */
public class CircleDrawingStrategy implements DrawingStrategy {

    @Override
    public void draw(GraphicsContext gc, double x, double y, double width, double height) {
        gc.setFill(Color.BLUE);
        gc.setStroke(Color.DARKBLUE);
        gc.setLineWidth(2);

        // Draw filled circle
        gc.fillOval(x, y, width, height);
        // Draw circle outline
        gc.strokeOval(x, y, width, height);

        // Log the drawing action
        LoggerSingleton.getInstance().log("INFO",
                "Circle drawn at (" + x + ", " + y + ") with size " + width + "x" + height);
    }

    @Override
    public String getShapeName() {
        return "Circle";
    }
}
