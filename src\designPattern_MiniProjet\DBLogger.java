package designPattern_MiniProjet;

import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;

/**
 * Concrete Strategy for Database Logging
 * Note: This is a mock implementation for demonstration
 */
public class DBLogger implements LoggerStrategy {
    private static final DateTimeFormatter formatter = DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss");
    private String connectionString;

    public DBLogger() {
        // Mock database connection string
        this.connectionString = "jdbc:sqlite:logs.db";
    }

    public DBLogger(String connectionString) {
        this.connectionString = connectionString;
    }

    @Override
    public void log(String message) {
        // Mock database logging - in real implementation, you would connect to actual
        // DB
        String timestamp = LocalDateTime.now().format(formatter);
        System.out.println("[DB] " + timestamp + " - Logging to database: " + message);

        // Uncomment below for actual database implementation
        /*
         * try (Connection conn = DriverManager.getConnection(connectionString)) {
         * String sql = "INSERT INTO logs (timestamp, message) VALUES (?, ?)";
         * PreparedStatement stmt = conn.prepareStatement(sql);
         * stmt.setString(1, timestamp);
         * stmt.setString(2, message);
         * stmt.executeUpdate();
         * } catch (SQLException e) {
         * System.err.println("Error logging to database: " + e.getMessage());
         * }
         */
    }
}
