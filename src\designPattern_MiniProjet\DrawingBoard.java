package designPattern_MiniProjet;

import javafx.scene.canvas.Canvas;
import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

/**
 * Main drawing board that uses all design patterns
 * Context class for Strategy pattern and Subject for Observer pattern
 */
public class DrawingBoard {
    private Canvas canvas;
    private GraphicsContext gc;
    private DrawingStrategy currentStrategy;
    private DrawingSubject drawingSubject;

    public DrawingBoard(double width, double height) {
        this.canvas = new Canvas(width, height);
        this.gc = canvas.getGraphicsContext2D();
        this.drawingSubject = new DrawingSubject();

        // Set default strategy
        this.currentStrategy = new CircleDrawingStrategy();

        // Initialize canvas
        clearCanvas();

        // Add default observers
        addObserver(new LogObserver("DrawingBoard Observer"));

        LoggerSingleton.getInstance().log("INFO", "DrawingBoard initialized with size " + width + "x" + height);
    }

    // Strategy Pattern - set drawing strategy
    public void setDrawingStrategy(DrawingStrategy strategy) {
        this.currentStrategy = strategy;
        LoggerSingleton.getInstance().log("INFO", "Drawing strategy changed to: " + strategy.getShapeName());
    }

    // Draw shape using current strategy
    public void drawShape(double x, double y, double width, double height) {
        if (currentStrategy != null) {
            currentStrategy.draw(gc, x, y, width, height);

            // Notify observers about the drawing event
            drawingSubject.setDrawingData(currentStrategy.getShapeName(), x, y, width, height);
        }
    }

    // Observer Pattern - add observer
    public void addObserver(DrawingObserver observer) {
        drawingSubject.addObserver(observer);
    }

    // Observer Pattern - remove observer
    public void removeObserver(DrawingObserver observer) {
        drawingSubject.removeObserver(observer);
    }

    // Clear the canvas
    public void clearCanvas() {
        gc.setFill(Color.WHITE);
        gc.fillRect(0, 0, canvas.getWidth(), canvas.getHeight());
        LoggerSingleton.getInstance().log("INFO", "Canvas cleared");
    }

    // Get the canvas for adding to scene
    public Canvas getCanvas() {
        return canvas;
    }

    // Get current strategy name
    public String getCurrentStrategyName() {
        return currentStrategy != null ? currentStrategy.getShapeName() : "None";
    }
}
