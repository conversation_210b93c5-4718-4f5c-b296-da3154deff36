package designPattern_MiniProjet;

import java.util.ArrayList;
import java.util.List;

/**
 * Subject in Observer Pattern - notifies observers about drawing events
 */
public class DrawingSubject {
    private List<DrawingObserver> observers;
    private String lastDrawnShape;
    private double lastX, lastY, lastWidth, lastHeight;

    public DrawingSubject() {
        this.observers = new ArrayList<>();
    }

    // Add observer
    public void addObserver(DrawingObserver observer) {
        observers.add(observer);
    }

    // Remove observer
    public void removeObserver(DrawingObserver observer) {
        observers.remove(observer);
    }

    // Notify all observers
    public void notifyObservers() {
        for (DrawingObserver observer : observers) {
            observer.update(lastDrawnShape, lastX, lastY, lastWidth, lastHeight);
        }
    }

    // Set drawing data and notify observers
    public void setDrawingData(String shapeName, double x, double y, double width, double height) {
        this.lastDrawnShape = shapeName;
        this.lastX = x;
        this.lastY = y;
        this.lastWidth = width;
        this.lastHeight = height;
        notifyObservers();
    }

    // Getters
    public String getLastDrawnShape() {
        return lastDrawnShape;
    }

    public double getLastX() {
        return lastX;
    }

    public double getLastY() {
        return lastY;
    }

    public double getLastWidth() {
        return lastWidth;
    }

    public double getLastHeight() {
        return lastHeight;
    }
}
