package designPattern_MiniProjet;

/**
 * Concrete Observer that logs drawing events
 */
public class LogObserver extends DrawingObserver {
    private String observerName;

    public LogObserver(String name) {
        this.observerName = name;
    }

    @Override
    public void update(String shapeName, double x, double y, double width, double height) {
        String message = observerName + " observed: " + shapeName +
                " drawn at (" + x + ", " + y + ") with size " + width + "x" + height;
        LoggerSingleton.getInstance().log("OBSERVER", message);
    }
}
