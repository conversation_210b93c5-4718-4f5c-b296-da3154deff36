package designPattern_MiniProjet;

/**
 * Singleton Pattern Implementation for Logger
 * Ensures only one instance of logger exists throughout the application
 */
public class LoggerSingleton {
    private static LoggerSingleton instance;
    private LoggerStrategy loggerStrategy;

    // Private constructor to prevent instantiation
    private LoggerSingleton() {
        // Default to console logging
        this.loggerStrategy = new ConsoleLogger();
    }

    // Thread-safe singleton instance creation
    public static synchronized LoggerSingleton getInstance() {
        if (instance == null) {
            instance = new LoggerSingleton();
        }
        return instance;
    }

    // Set logging strategy
    public void setLoggerStrategy(LoggerStrategy strategy) {
        this.loggerStrategy = strategy;
    }

    // Log message using current strategy
    public void log(String message) {
        if (loggerStrategy != null) {
            loggerStrategy.log(message);
        }
    }

    // Log with level
    public void log(String level, String message) {
        if (loggerStrategy != null) {
            loggerStrategy.log("[" + level + "] " + message);
        }
    }
}
