package designPattern_MiniProjet;

/**
 * Simple test class to verify all design patterns work correctly
 * This can be run without JavaFX to test the core functionality
 */
public class PatternTest {

    public static void main(String[] args) {
        System.out.println("=== Design Patterns Test ===");

        // Test Singleton Pattern
        testSingletonPattern();

        // Test Strategy Pattern
        testStrategyPattern();

        // Test Observer Pattern
        testObserverPattern();

        // Test Decorator Pattern
        testDecoratorPattern();

        // Test Adapter Pattern
        testAdapterPattern();

        System.out.println("=== All Tests Completed ===");
    }

    private static void testSingletonPattern() {
        System.out.println("\n1. Testing Singleton Pattern:");

        LoggerSingleton logger1 = LoggerSingleton.getInstance();
        LoggerSingleton logger2 = LoggerSingleton.getInstance();

        System.out.println("Same instance? " + (logger1 == logger2));
        logger1.log("Test message from singleton");

        // Test different strategies
        logger1.setLoggerStrategy(new FileLogger("test.log"));
        logger1.log("Test message to file");

        logger1.setLoggerStrategy(new DBLogger());
        logger1.log("Test message to database");

        // Reset to console
        logger1.setLoggerStrategy(new ConsoleLogger());
    }

    private static void testStrategyPattern() {
        System.out.println("\n2. Testing Strategy Pattern:");

        // Test logger strategies instead (no JavaFX dependency)
        LoggerStrategy console = new ConsoleLogger();
        LoggerStrategy file = new FileLogger("strategy_test.log");
        LoggerStrategy db = new DBLogger();

        System.out.println("Testing console logger:");
        console.log("Console strategy test message");

        System.out.println("Testing file logger:");
        file.log("File strategy test message");

        System.out.println("Testing database logger:");
        db.log("Database strategy test message");

        System.out.println("Strategy pattern working correctly");
    }

    private static void testObserverPattern() {
        System.out.println("\n3. Testing Observer Pattern:");

        DrawingSubject subject = new DrawingSubject();
        LogObserver observer1 = new LogObserver("Observer1");
        LogObserver observer2 = new LogObserver("Observer2");

        subject.addObserver(observer1);
        subject.addObserver(observer2);

        // Trigger notification
        subject.setDrawingData("TestShape", 10, 20, 50, 60);

        System.out.println("Observer pattern notifications sent");
    }

    private static void testDecoratorPattern() {
        System.out.println("\n4. Testing Decorator Pattern:");

        DrawingStrategy basicCircle = new CircleDrawingStrategy();
        DrawingStrategy decoratedCircle = new BorderDecorator(basicCircle);

        System.out.println("Basic shape: " + basicCircle.getShapeName());
        System.out.println("Decorated shape: " + decoratedCircle.getShapeName());

        // Test nested decoration
        DrawingStrategy doubleDecorated = new BorderDecorator(decoratedCircle);
        System.out.println("Double decorated: " + doubleDecorated.getShapeName());
    }

    private static void testAdapterPattern() {
        System.out.println("\n5. Testing Adapter Pattern:");

        DrawingStrategy triangleAdapter = new ShapeAdapter("triangle");
        DrawingStrategy diamondAdapter = new ShapeAdapter("diamond");

        System.out.println("Triangle adapter: " + triangleAdapter.getShapeName());
        System.out.println("Diamond adapter: " + diamondAdapter.getShapeName());

        System.out.println("Legacy shapes adapted successfully");
    }
}
