package designPattern_MiniProjet;

/**
 * Simple test class to verify core design patterns work correctly
 * This tests patterns without JavaFX dependencies
 */
public class PatternTest {

    public static void main(String[] args) {
        System.out.println("=== Design Patterns Test ===");

        // Test Singleton Pattern
        testSingletonPattern();

        // Test Strategy Pattern (Logger strategies)
        testLoggerStrategyPattern();

        // Test Observer Pattern
        testObserverPattern();

        System.out.println("=== All Core Tests Completed ===");
        System.out.println("Note: Drawing strategies, decorators, and adapters require JavaFX to test fully.");
    }

    private static void testSingletonPattern() {
        System.out.println("\n1. Testing Singleton Pattern:");

        LoggerSingleton logger1 = LoggerSingleton.getInstance();
        LoggerSingleton logger2 = LoggerSingleton.getInstance();

        System.out.println("Same instance? " + (logger1 == logger2));
        logger1.log("Test message from singleton");

        // Test different strategies
        logger1.setLoggerStrategy(new FileLogger("test.log"));
        logger1.log("Test message to file");

        logger1.setLoggerStrategy(new DBLogger());
        logger1.log("Test message to database");

        // Reset to console
        logger1.setLoggerStrategy(new ConsoleLogger());
    }

    private static void testLoggerStrategyPattern() {
        System.out.println("\n2. Testing Strategy Pattern (Logger Strategies):");

        // Test logger strategies (no JavaFX dependency)
        LoggerStrategy console = new ConsoleLogger();
        LoggerStrategy file = new FileLogger("strategy_test.log");
        LoggerStrategy db = new DBLogger();

        System.out.println("Testing console logger:");
        console.log("Console strategy test message");

        System.out.println("Testing file logger:");
        file.log("File strategy test message");

        System.out.println("Testing database logger:");
        db.log("Database strategy test message");

        System.out.println("Strategy pattern working correctly");
    }

    private static void testObserverPattern() {
        System.out.println("\n3. Testing Observer Pattern:");

        DrawingSubject subject = new DrawingSubject();
        LogObserver observer1 = new LogObserver("Observer1");
        LogObserver observer2 = new LogObserver("Observer2");

        subject.addObserver(observer1);
        subject.addObserver(observer2);

        // Trigger notification
        subject.setDrawingData("TestShape", 10, 20, 50, 60);

        System.out.println("Observer pattern notifications sent");
    }

}
