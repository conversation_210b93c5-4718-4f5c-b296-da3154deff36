package designPattern_MiniProjet;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

/**
 * Concrete Strategy for drawing rectangles
 */
public class RectangleDrawingStrategy implements DrawingStrategy {

    @Override
    public void draw(GraphicsContext gc, double x, double y, double width, double height) {
        gc.setFill(Color.RED);
        gc.setStroke(Color.DARKRED);
        gc.setLineWidth(2);

        // Draw filled rectangle
        gc.fillRect(x, y, width, height);
        // Draw rectangle outline
        gc.strokeRect(x, y, width, height);

        // Log the drawing action
        LoggerSingleton.getInstance().log("INFO",
                "Rectangle drawn at (" + x + ", " + y + ") with size " + width + "x" + height);
    }

    @Override
    public String getShapeName() {
        return "Rectangle";
    }
}
