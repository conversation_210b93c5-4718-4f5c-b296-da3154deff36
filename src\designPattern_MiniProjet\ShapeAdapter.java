package designPattern_MiniProjet;

import javafx.scene.canvas.GraphicsContext;
import javafx.scene.paint.Color;

/**
 * Adapter Pattern - Adapts legacy drawing methods to new DrawingStrategy
 * interface
 * This simulates adapting an old drawing system to work with our new strategy
 * pattern
 */
public class ShapeAdapter implements DrawingStrategy {
    private LegacyShapeDrawer legacyDrawer;
    private String shapeType;

    public ShapeAdapter(String shapeType) {
        this.legacyDrawer = new LegacyShapeDrawer();
        this.shapeType = shapeType;
    }

    @Override
    public void draw(GraphicsContext gc, double x, double y, double width, double height) {
        // Adapt the legacy drawing method to our new interface
        switch (shapeType.toLowerCase()) {
            case "triangle":
                legacyDrawer.drawTriangle(gc, x, y, width, height);
                break;
            case "diamond":
                legacyDrawer.drawDiamond(gc, x, y, width, height);
                break;
            default:
                legacyDrawer.drawTriangle(gc, x, y, width, height);
        }

        LoggerSingleton.getInstance().log("ADAPTER", "Adapted legacy " + shapeType + " drawing");
    }

    @Override
    public String getShapeName() {
        return "Legacy " + shapeType;
    }

    /**
     * Legacy drawing class that we're adapting
     */
    private static class LegacyShapeDrawer {
        public void drawTriangle(GraphicsContext gc, double x, double y, double width, double height) {
            gc.setFill(Color.GREEN);
            gc.setStroke(Color.DARKGREEN);
            gc.setLineWidth(2);

            // Draw triangle using polygon
            double[] xPoints = { x + width / 2, x, x + width };
            double[] yPoints = { y, y + height, y + height };
            gc.fillPolygon(xPoints, yPoints, 3);
            gc.strokePolygon(xPoints, yPoints, 3);
        }

        public void drawDiamond(GraphicsContext gc, double x, double y, double width, double height) {
            gc.setFill(Color.PURPLE);
            gc.setStroke(Color.DARKMAGENTA);
            gc.setLineWidth(2);

            // Draw diamond using polygon
            double[] xPoints = { x + width / 2, x + width, x + width / 2, x };
            double[] yPoints = { y, y + height / 2, y + height, y + height / 2 };
            gc.fillPolygon(xPoints, yPoints, 4);
            gc.strokePolygon(xPoints, yPoints, 4);
        }
    }
}
