package designPattern_MiniProjet;

import javafx.scene.canvas.GraphicsContext;

/**
 * Abstract Decorator in Decorator Pattern
 * Wraps a DrawingStrategy to add additional functionality
 */
public abstract class ShapeDecorator implements DrawingStrategy {
    protected DrawingStrategy decoratedStrategy;

    public ShapeDecorator(DrawingStrategy strategy) {
        this.decoratedStrategy = strategy;
    }

    @Override
    public void draw(GraphicsContext gc, double x, double y, double width, double height) {
        decoratedStrategy.draw(gc, x, y, width, height);
    }

    @Override
    public String getShapeName() {
        return decoratedStrategy.getShapeName();
    }
}
