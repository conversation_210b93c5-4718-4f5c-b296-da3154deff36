package designPattern_MiniProjet;

import javafx.application.Application;
import javafx.geometry.Insets;
import javafx.scene.Scene;
import javafx.scene.control.Button;
import javafx.scene.control.ComboBox;
import javafx.scene.control.Label;
import javafx.scene.layout.HBox;
import javafx.scene.layout.VBox;
import javafx.stage.Stage;

/**
 * Main application demonstrating all design patterns:
 * - Singleton: LoggerSingleton
 * - Strategy: Different drawing strategies
 * - Observer: Drawing event observers
 * - Decorator: Shape decorators
 * - Adapter: Legacy shape adapter
 */
public class test extends Application {
    private DrawingBoard drawingBoard;
    private ComboBox<String> strategyComboBox;
    private ComboBox<String> loggerComboBox;

    @Override
    public void start(Stage primaryStage) {
        primaryStage.setTitle("Design Patterns Demo - Drawing Application");

        // Initialize drawing board
        drawingBoard = new DrawingBoard(600, 400);

        // Create UI controls
        VBox root = createUI();

        // Set up the scene
        Scene scene = new Scene(root, 800, 500);
        primaryStage.setScene(scene);
        primaryStage.show();

        // Log application start
        LoggerSingleton.getInstance().log("INFO", "Design Patterns Demo Application started");

        // Demonstrate all patterns
        demonstratePatterns();
    }

    private VBox createUI() {
        VBox root = new VBox(10);
        root.setPadding(new Insets(10));

        // Title
        Label title = new Label("Design Patterns Demo - Drawing Application");
        title.setStyle("-fx-font-size: 18px; -fx-font-weight: bold;");

        // Strategy selection
        HBox strategyBox = new HBox(10);
        strategyBox.getChildren().add(new Label("Drawing Strategy:"));
        strategyComboBox = new ComboBox<>();
        strategyComboBox.getItems().addAll(
                "Circle", "Rectangle", "Triangle (Adapter)", "Diamond (Adapter)",
                "Bordered Circle", "Bordered Rectangle");
        strategyComboBox.setValue("Circle");
        strategyComboBox.setOnAction(e -> changeStrategy());
        strategyBox.getChildren().add(strategyComboBox);

        // Logger selection
        HBox loggerBox = new HBox(10);
        loggerBox.getChildren().add(new Label("Logger Strategy:"));
        loggerComboBox = new ComboBox<>();
        loggerComboBox.getItems().addAll("Console", "File", "Database");
        loggerComboBox.setValue("Console");
        loggerComboBox.setOnAction(e -> changeLogger());
        loggerBox.getChildren().add(loggerComboBox);

        // Action buttons
        HBox buttonBox = new HBox(10);
        Button drawButton = new Button("Draw Random Shape");
        drawButton.setOnAction(e -> drawRandomShape());

        Button clearButton = new Button("Clear Canvas");
        clearButton.setOnAction(e -> drawingBoard.clearCanvas());

        Button demoButton = new Button("Run Pattern Demo");
        demoButton.setOnAction(e -> demonstratePatterns());

        buttonBox.getChildren().addAll(drawButton, clearButton, demoButton);

        // Add all components
        root.getChildren().addAll(title, strategyBox, loggerBox, buttonBox, drawingBoard.getCanvas());

        return root;
    }

    private void changeStrategy() {
        String selected = strategyComboBox.getValue();
        DrawingStrategy strategy;

        switch (selected) {
            case "Circle":
                strategy = new CircleDrawingStrategy();
                break;
            case "Rectangle":
                strategy = new RectangleDrawingStrategy();
                break;
            case "Triangle (Adapter)":
                strategy = new ShapeAdapter("triangle");
                break;
            case "Diamond (Adapter)":
                strategy = new ShapeAdapter("diamond");
                break;
            case "Bordered Circle":
                strategy = new BorderDecorator(new CircleDrawingStrategy());
                break;
            case "Bordered Rectangle":
                strategy = new BorderDecorator(new RectangleDrawingStrategy());
                break;
            default:
                strategy = new CircleDrawingStrategy();
        }

        drawingBoard.setDrawingStrategy(strategy);
    }

    private void changeLogger() {
        String selected = loggerComboBox.getValue();
        LoggerStrategy logger;

        switch (selected) {
            case "Console":
                logger = new ConsoleLogger();
                break;
            case "File":
                logger = new FileLogger("demo_app.log");
                break;
            case "Database":
                logger = new DBLogger();
                break;
            default:
                logger = new ConsoleLogger();
        }

        LoggerSingleton.getInstance().setLoggerStrategy(logger);
        LoggerSingleton.getInstance().log("INFO", "Logger strategy changed to: " + selected);
    }

    private void drawRandomShape() {
        // Generate random position and size
        double x = Math.random() * (drawingBoard.getCanvas().getWidth() - 100);
        double y = Math.random() * (drawingBoard.getCanvas().getHeight() - 100);
        double width = 50 + Math.random() * 100;
        double height = 50 + Math.random() * 100;

        drawingBoard.drawShape(x, y, width, height);
    }

    private void demonstratePatterns() {
        LoggerSingleton.getInstance().log("DEMO", "=== Design Patterns Demonstration ===");

        // Clear canvas first
        drawingBoard.clearCanvas();

        // Demonstrate Strategy Pattern
        LoggerSingleton.getInstance().log("DEMO", "1. Strategy Pattern - Different drawing strategies");

        // Circle strategy
        drawingBoard.setDrawingStrategy(new CircleDrawingStrategy());
        drawingBoard.drawShape(50, 50, 80, 80);

        // Rectangle strategy
        drawingBoard.setDrawingStrategy(new RectangleDrawingStrategy());
        drawingBoard.drawShape(150, 50, 100, 60);

        // Adapter Pattern - Legacy shapes
        LoggerSingleton.getInstance().log("DEMO", "2. Adapter Pattern - Legacy shape integration");
        drawingBoard.setDrawingStrategy(new ShapeAdapter("triangle"));
        drawingBoard.drawShape(270, 50, 80, 80);

        drawingBoard.setDrawingStrategy(new ShapeAdapter("diamond"));
        drawingBoard.drawShape(370, 50, 80, 80);

        // Decorator Pattern
        LoggerSingleton.getInstance().log("DEMO", "3. Decorator Pattern - Adding borders");
        drawingBoard.setDrawingStrategy(new BorderDecorator(new CircleDrawingStrategy()));
        drawingBoard.drawShape(50, 150, 80, 80);

        drawingBoard.setDrawingStrategy(new BorderDecorator(new RectangleDrawingStrategy()));
        drawingBoard.drawShape(150, 150, 100, 60);

        // Observer Pattern is automatically demonstrated through logging
        LoggerSingleton.getInstance().log("DEMO", "4. Observer Pattern - Automatic logging of all drawing events");

        // Singleton Pattern
        LoggerSingleton.getInstance().log("DEMO", "5. Singleton Pattern - Single logger instance used throughout");

        // Test different logger strategies
        LoggerSingleton.getInstance().log("DEMO", "Testing different logger strategies...");

        // Switch to file logger
        LoggerSingleton.getInstance().setLoggerStrategy(new FileLogger("pattern_demo.log"));
        LoggerSingleton.getInstance().log("DEMO", "This message goes to file");

        // Switch back to console
        LoggerSingleton.getInstance().setLoggerStrategy(new ConsoleLogger());
        LoggerSingleton.getInstance().log("DEMO", "Back to console logging");

        LoggerSingleton.getInstance().log("DEMO", "=== Demonstration Complete ===");
    }

    public static void main(String[] args) {
        launch(args);
    }
}
